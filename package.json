{"name": "redact-app", "version": "2.4.1", "main": "src/index.tsx", "author": "Veritone", "private": true, "scripts": {"start": "npm run start:redact", "start:redact": "REACT_APP_BUILD_APP=redact webpack serve --config webpack.dev.js --env development", "start:cbsa": "REACT_APP_BUILD_APP=cbsa webpack serve --config webpack.dev.js --env development", "startssl": "npm run startssl:redact", "startssl:redact": "webpack serve --config webpack.dev.js --env development --server-type https --server-options-key ./local.veritone.com-key.pem --server-options-cert ./local.veritone.com.pem", "startssl:cypress": "CYPRESS_TEST=true yarn run startssl", "startssl:cbsa": "REACT_APP_BUILD_APP=cbsa webpack serve --config webpack.dev.js --env development --server-type https --server-options-key ./local.veritone.com-key.pem --server-options-cert ./local.veritone.com.pem", "build": "npm run build:redact", "build:redact": "REACT_APP_BUILD_APP=redact webpack --config webpack.prod.js --env production", "build:cbsa": "REACT_APP_BUILD_APP=cbsa  webpack --config webpack.prod.js --env production", "test": "yarn test:redact && yarn test:cbsa && yarn test:common && yarn test:server && yarn test:utils", "test:parallel": "concurrently yarn:test:redact yarn:test:cbsa yarn:test:common yarn:test:server", "test:update": "concurrently \"yarn run test:update:common\" \"yarn run test:update:redact\" \"yarn run test:update:cbsa\" \"yarn run test:update:server\"", "test:redact": "TZ=UTC jest --verbose ./test/redact-app/unit", "test:update:redact": "TZ=UTC jest -u --verbose ./test/redact-app/unit", "test:server": "TZ=UTC && jest --verbose ./server/test", "test:update:server": "TZ=UTC && jest -u --verbose ./server/test", "test:cbsa": "TZ=UTC jest --verbose ./test/cbsa-app/unit", "test:update:cbsa": "TZ=UTC jest -u --verbose ./test/cbsa-app/unit", "test:common": "TZ=UTC jest --verbose ./test/common-app/unit", "test:update:common": "TZ=UTC jest -u --verbose ./test/common-app/unit", "test:utils": "TZ=UTC jest --verbose ./test/utils", "format": "prettier --write \"./**/*.{ts,tsx,js,css,scss,md,json}\"", "check-licenses": "yarn license-checker", "lint": "concurrently \"yarn run lint:tsc\" \"yarn run lint:all\" \"yarn run lint:styles\"", "lint:ts": "eslint \"./src/**/*.{ts,tsx}\" --max-warnings 0", "lint:tsc": "tsc -p tsconfig.json --noEmit --checkJs false", "lint:js": "eslint \"./src/**/!(*.test).js\" --max-warnings 0", "lint:all": "eslint \"./src/**/!(*.test).{ts,tsx,js,jsx}\" \"./cypress/**/*.{ts,tsx,js,jsx}\" --max-warnings 0", "lint:fix": "eslint \"./src/**/!(*.test).{ts,tsx,js,jsx}\" \"./cypress/**/*.{ts,tsx,js,jsx}\" --max-warnings 0 --fix", "lint:styles": "stylelint \"src/**/*.scss\"", "lint:test": "eslint \"./test/**/*.{ts,tsx,js,jsx}\" \"./src/**/*.test.{ts,tsx,js,jsx}\"", "clean": "rm -rf .awcache/ && rm -rf node_modules/ && rm -f yarn.lock && yarn", "report": "./node_modules/.bin/allure serve ./test_e2e/output", "test:e2e": "./node_modules/.bin/codeceptjs run --verbose", "test-parallel:e2e": "./node_modules/.bin/codeceptjs run-multiple parallel --verbose", "test:accumulate": "node ./test_e2e/accumulate_report.js", "test:  coverage": "node ./translate-coverage.js && ./node_modules/.bin/nyc report --reporter=html", "e2e": "cypress run --env ENVIRONMENT=azure-stage", "cy:open": "cypress open", "cy:run": "cypress run --browser chrome", "cy:runLocal": "cypress run --browser chrome --config baseUrl=https://local.veritone.com:3001", "cy:runLocal:exclude-health": "cypress run --browser chrome --config baseUrl=https://local.veritone.com:3001 --env grepTags='not @health-check'", "cy:report": "node ./cypress/reporter-config.ts", "cy:run-report": "yarn cy:run; yarn cy:report", "prepare": "husky install"}, "devDependencies": {"@babel/core": "^7.27.7", "@babel/eslint-parser": "~7.27.5", "@babel/plugin-proposal-decorators": "^7.27.1", "@babel/plugin-transform-modules-commonjs": "^7.27.1", "@babel/plugin-transform-runtime": "^7.27.4", "@babel/preset-env": "^7.27.2", "@babel/preset-react": "^7.27.1", "@babel/preset-typescript": "^7.27.1", "@badeball/cypress-cucumber-preprocessor": "^22.2.0", "@cspell/dict-fr-fr": "^2.3.0", "@cspell/eslint-plugin": "^9.1.2", "@cypress/webpack-preprocessor": "^6.0.4", "@eslint/compat": "^1.3.1", "@eslint/eslintrc": "^3.3.1", "@eslint/js": "^9.29.0", "@jambit/eslint-plugin-typed-redux-saga": "^0.4.0", "@jest-mock/express": "^2.1.0", "@redux-saga/testing-utils": "^1.1.5", "@sentry/webpack-plugin": "^3.5.0", "@svgr/webpack": "^8.1.0", "@testing-library/dom": "^10.4.0", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.3.0", "@testing-library/user-event": "^14.6.1", "@types/debug": "^4.1.12", "@types/file-saver": "^2.0.7", "@types/jest": "^29.5.14", "@types/license-checker": "^25.0.6", "@types/lodash": "~4.17.19", "@types/node": "^22.15.33", "@types/papaparse": "^5.3.16", "@types/pluralize": "^0.0.33", "@types/react": "^18.3.23", "@types/react-dom": "^18.3.7", "@types/react-router-dom": "^5.3.3", "@types/react-virtualized": "^9.22.2", "@types/redux-api-middleware": "^3.2.7", "@types/redux-first-router": "~2.1.12", "@types/redux-logger": "^3.0.13", "@types/redux-mock-store": "^1.5.0", "@types/resize-observer-browser": "^0.1.11", "@types/uuid": "10.0.0", "@types/wicg-file-system-access": "~2023.10.6", "autoprefixer": "~10.4.21", "babel-jest": "^29.7.0", "babel-loader": "^10.0.0", "babel-plugin-explicit-exports-references": "^1.0.2", "babel-plugin-jsx-remove-data-test-id": "^3.0.0", "babel-plugin-macros": "^3.1.0", "babel-plugin-transform-import-meta": "^2.3.3", "canvas": "^3.1.2", "clean-webpack-plugin": "^4.0.0", "concurrently": "^9.2.0", "css-loader": "^7.1.2", "css-minimizer-webpack-plugin": "^7.0.2", "cypress": "14.1.0", "cypress-cucumber-preprocessor": "^4.3.1", "cypress-file-upload": "^5.0.8", "cypress-promise": "^1.1.0", "eslint": "^9.29.0", "eslint-config-prettier": "^10.1.5", "eslint-import-resolver-typescript": "^4.4.4", "eslint-plugin-cypress": "^5.1.0", "eslint-plugin-formatjs": "^5.4.0", "eslint-plugin-import": "^2.32.0", "eslint-plugin-jest-dom": "^5.5.0", "eslint-plugin-lodash": "^8.0.0", "eslint-plugin-prettier": "^5.5.1", "eslint-plugin-promise": "^7.2.1", "eslint-plugin-react": "^7.37.5", "eslint-plugin-react-hooks": "5.2.0", "eslint-plugin-testing-library": "^7.5.3", "fork-ts-checker-webpack-plugin": "^9.1.0", "globals": "^16.2.0", "html-webpack-plugin": "^5.6.3", "husky": "^9.1.7", "identity-obj-proxy": "~3.0.0", "jest": "^29.7.0", "jest-canvas-mock": "^2.5.2", "jest-css-modules-transform": "^4.4.2", "jest-environment-jsdom": "^29.7.0", "jest-module-name-mapper": "^0.1.5", "jest-transform-stub": "^2.0.0", "mini-css-extract-plugin": "^2.9.2", "multiple-cucumber-html-reporter": "^3.9.3", "node-fetch": "^2.7.0", "path-browserify": "^1.0.1", "postcss": "^8.5.6", "postcss-loader": "^8.1.1", "prettier": "^3.6.2", "redux-mock-store": "^1.5.5", "sass-embedded": "^1.89.2", "sass-loader": "^16.0.5", "style-loader": "~4.0.0", "stylelint": "^16.21.0", "stylelint-config-standard-scss": "^15.0.1", "ts-essentials": "^10.1.1", "ts-loader": "^9.5.2", "ts-node": "^10.9.2", "tsconfig-paths-webpack-plugin": "^4.2.0", "tslib": "^2.8.1", "typescript": "^5.8.3", "typescript-eslint": "^8.35.0", "url-loader": "^4.1.1", "webpack": "^5.99.9", "webpack-cli": "^6.0.1", "webpack-dev-server": "^5.2.2", "webpack-merge": "^6.0.1"}, "dependencies": {"@emotion/react": "^11.14.0", "@emotion/styled": "^11.14.0", "@fontsource/roboto": "^5.1.1", "@mui/icons-material": "6.3.1", "@mui/material": "6.3.1", "@mui/styles": "6.3.1", "@mui/x-date-pickers": "^7.23.3", "@reduxjs/toolkit": "2.8.2", "@sentry/browser": "^9.10.1", "@supercharge/promise-pool": "^3.2.0", "@veritone/glc-react": "^1.2.6", "@veritone/glc-redux": "1.0.15", "classnames": "^2.5.1", "csp-html-webpack-plugin": "^5.1.0", "date-fns": "^3.6.0", "debug": "^4.4.0", "fast-deep-equal": "^3.1.3", "file-saver": "^2.0.5", "fp-ts": "~2.16.9", "framer-motion": "^6.5.1", "konva": "9.3.0", "license-checker": "^25.0.1", "lodash": "~4.17.21", "memoize-one": "^6.0.0", "mime-types": "^2.1.35", "moment": "^2.30.1", "monocle-ts": "2.3.13", "notistack": "^3.0.2", "papaparse": "^5.5.2", "pluralize": "^8.0.0", "prop-types": "15.8.1", "query-string": "~7.1.3", "react": "^18.3.1", "react-dom": "^18.3.1", "react-draggable": "4.4.6", "react-dropzone": "^14.3.8", "react-hook-form": "^7.54.2", "react-intl": "7.1.11", "react-jss": "^10.10.0", "react-moment": "^1.1.3", "react-redux": "^9.2.0", "react-rnd": "10.4.13", "react-router-dom": "^6.30.0", "react-toastify": "^10.0.6", "react-transition-group": "^4.4.5", "react-virtualized": "^9.22.6", "react-virtuoso": "^4.12.5", "redux-api-middleware": "^3.2.1", "redux-first-router": "~2.1.5", "redux-logger": "3.0.6", "redux-saga": "^1.3.0", "redux-thunk": "^3.1.0", "release-it": "^19.0.2", "rxjs": "7.8.1", "shaka-player": "~4.3.16", "typed-redux-saga": "~1.5.0", "uuid": "^10.0.0", "veritone-types": "git+https://github.com/veritone/veritone-sdk-types.git#v0.7.1", "video-react": "0.16.0"}, "resolutions": {"domutils": "^3.1.0", "react": "^18.3.1", "react-dom": "^18.3.1", "@types/react": "^18.3.23", "@types/react-dom": "^18.3.7", "redux": "^5.0.1", "react-redux": "^9.2.0", "shaka-player": "~4.3.15", "redux-saga": "^1.3.0", "semver": "^7.5.4", "csp-html-webpack-plugin/cheerio": "1.0.0-rc.10"}, "packageManager": "yarn@4.8.1", "stepDefinitions": "cypress/e2e/**/[filepath]/*.{js,ts}", "cypress-cucumber-preprocessor": {"json": {"enabled": true, "output": "cypress/reports/cucumber-json.json"}}}